// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journal_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$journalRepositoryHash() => r'd0e2450be7c5c3e93a78eabfe89949d55e3e7926';

/// Journal repository provider
///
/// Copied from [journalRepository].
@ProviderFor(journalRepository)
final journalRepositoryProvider =
    AutoDisposeProvider<JournalRepository>.internal(
  journalRepository,
  name: r'journalRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalRepositoryRef = AutoDisposeProviderRef<JournalRepository>;
String _$journalServiceHash() => r'ddd4cb94d4afd631da1b5ce6e2710bd3dd69089f';

/// Journal service provider
///
/// Copied from [journalService].
@ProviderFor(journalService)
final journalServiceProvider = AutoDisposeProvider<JournalService>.internal(
  journalService,
  name: r'journalServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalServiceRef = AutoDisposeProviderRef<JournalService>;
String _$journalStreamHash() => r'd62109e82e04aa11c168fb32667f2b512ffd0132';

/// Journal stream provider for real-time updates
///
/// Copied from [journalStream].
@ProviderFor(journalStream)
final journalStreamProvider =
    AutoDisposeStreamProvider<List<JournalEntryModel>>.internal(
  journalStream,
  name: r'journalStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JournalStreamRef
    = AutoDisposeStreamProviderRef<List<JournalEntryModel>>;
String _$journalNotifierHash() => r'08aa5da9158f2eb08faa8f157c8c9d750693c7b0';

/// Journal state notifier for managing journal entries
///
/// Copied from [JournalNotifier].
@ProviderFor(JournalNotifier)
final journalNotifierProvider = AutoDisposeNotifierProvider<JournalNotifier,
    AsyncValue<JournalState>>.internal(
  JournalNotifier.new,
  name: r'journalNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JournalNotifier = AutoDisposeNotifier<AsyncValue<JournalState>>;
String _$journalFormNotifierHash() =>
    r'8a7245ad8687c8df85d6dbaa7b082fb3f04a2db8';

/// Journal form state notifier
///
/// Copied from [JournalFormNotifier].
@ProviderFor(JournalFormNotifier)
final journalFormNotifierProvider =
    AutoDisposeNotifierProvider<JournalFormNotifier, JournalFormState>.internal(
  JournalFormNotifier.new,
  name: r'journalFormNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journalFormNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JournalFormNotifier = AutoDisposeNotifier<JournalFormState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
