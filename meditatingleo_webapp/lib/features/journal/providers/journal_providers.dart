import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../data/models/journal_entry_model.dart';
import '../data/repositories/journal_repository.dart';
import '../data/services/journal_service.dart';
import '../../../shared/models/result.dart';
import '../../../shared/models/app_error.dart';

part 'journal_providers.freezed.dart';
part 'journal_providers.g.dart';

/// Journal repository provider
@riverpod
JournalRepository journalRepository(JournalRepositoryRef ref) {
  final journalService = ref.watch(journalServiceProvider);
  return JournalRepository(journalService);
}

/// Journal service provider
@riverpod
JournalService journalService(JournalServiceRef ref) {
  // For now, return a mock service for testing
  return _MockJournalService();
}

/// Mock journal service for testing
class _MockJournalService implements JournalService {
  @override
  Future<Result<JournalEntryModel, AppError>> createEntry(
      JournalEntryModel entry) async {
    return Result.success(entry.copyWith(id: 'mock_id'));
  }

  @override
  Future<Result<List<JournalEntryModel>, AppError>> getAllEntries() async {
    return const Result.success([]);
  }

  @override
  Future<Result<JournalEntryModel?, AppError>> getEntry(String entryId) async {
    return const Result.success(null);
  }

  @override
  Future<Result<JournalEntryModel, AppError>> updateEntry(
      JournalEntryModel entry) async {
    return Result.success(entry);
  }

  @override
  Future<Result<bool, AppError>> deleteEntry(String entryId) async {
    return const Result.success(true);
  }

  @override
  Stream<List<JournalEntryModel>> watchEntries() {
    return Stream.value([]);
  }

  // Implement other required methods with mock responses
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

/// Journal state notifier for managing journal entries
@riverpod
class JournalNotifier extends _$JournalNotifier {
  @override
  AsyncValue<JournalState> build() {
    return const AsyncValue.data(JournalState());
  }

  /// Load all journal entries
  Future<void> loadEntries() async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(journalRepositoryProvider);
      final result = await repository.getAllEntries();

      result.when(
        success: (entries) {
          state = AsyncValue.data(JournalState(entries: entries));
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Create a new journal entry
  Future<void> createEntry(String title, String content) async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(journalRepositoryProvider);
      final newEntry = JournalEntryModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        content: content,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        userId: 'current_user', // TODO: Get from auth provider
      );

      final result = await repository.createEntry(newEntry);

      result.when(
        success: (entry) {
          // Reload entries to get updated list
          loadEntries();
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Update an existing journal entry
  Future<void> updateEntry(String id, String title, String content) async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(journalRepositoryProvider);
      final updatedEntry = JournalEntryModel(
        id: id,
        title: title,
        content: content,
        createdAt: DateTime.now(), // TODO: Preserve original created date
        updatedAt: DateTime.now(),
        userId: 'current_user', // TODO: Get from auth provider
      );

      final result = await repository.updateEntry(updatedEntry);

      result.when(
        success: (entry) {
          // Reload entries to get updated list
          loadEntries();
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Delete a journal entry
  Future<void> deleteEntry(String id) async {
    state = const AsyncValue.loading();

    try {
      final repository = ref.read(journalRepositoryProvider);
      final result = await repository.deleteEntry(id);

      result.when(
        success: (_) {
          // Reload entries to get updated list
          loadEntries();
        },
        failure: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Journal stream provider for real-time updates
@riverpod
Stream<List<JournalEntryModel>> journalStream(JournalStreamRef ref) {
  final repository = ref.watch(journalRepositoryProvider);
  return repository.watchEntries();
}

/// Journal form state notifier
@riverpod
class JournalFormNotifier extends _$JournalFormNotifier {
  @override
  JournalFormState build() {
    return const JournalFormState();
  }

  /// Update the title field
  void updateTitle(String title) {
    state = state.copyWith(
      title: title,
      isValid: _validateForm(title, state.content),
    );
  }

  /// Update the content field
  void updateContent(String content) {
    state = state.copyWith(
      content: content,
      isValid: _validateForm(state.title, content),
    );
  }

  /// Clear the form
  void clearForm() {
    state = const JournalFormState();
  }

  /// Set form data for editing
  void setFormData(String title, String content) {
    state = state.copyWith(
      title: title,
      content: content,
      isValid: _validateForm(title, content),
    );
  }

  /// Validate the form
  bool _validateForm(String title, String content) {
    return title.trim().isNotEmpty && content.trim().isNotEmpty;
  }
}

/// Journal state model
@freezed
class JournalState with _$JournalState {
  const factory JournalState({
    @Default([]) List<JournalEntryModel> entries,
    @Default(false) bool isLoading,
    String? errorMessage,
  }) = _JournalState;
}

/// Journal form state model
@freezed
class JournalFormState with _$JournalFormState {
  const factory JournalFormState({
    @Default('') String title,
    @Default('') String content,
    @Default(false) bool isValid,
    @Default(false) bool isSubmitting,
    String? errorMessage,
  }) = _JournalFormState;
}
