import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter/foundation.dart';

part 'web_cache_service.g.dart';

/// Web cache service provider
@riverpod
WebCacheService webCacheService(Ref ref) {
  return WebCacheService();
}

/// Web-specific cache service using browser storage APIs
class WebCacheService {
  static const String _keyPrefix = 'meditatingleo_';

  // Mock storage for testing
  final Map<String, String> _localStorage = {};
  final Map<String, String> _sessionStorage = {};

  /// Initialize the cache service
  Future<bool> initialize() async {
    try {
      // For testing, just return true
      if (kIsWeb) {
        // In web environment, would use actual localStorage
        // For now, use mock storage
      }
      return true;
    } catch (e) {
      throw Exception('LocalStorage not available: $e');
    }
  }

  /// Set data in localStorage
  Future<bool> set(String key, dynamic data) async {
    try {
      final jsonString = jsonEncode(data);
      _localStorage['$_keyPrefix$key'] = jsonString;
      return true;
    } catch (e) {
      throw Exception('Failed to set cache data: $e');
    }
  }

  /// Get data from localStorage
  Future<dynamic> get(String key) async {
    try {
      final jsonString = _localStorage['$_keyPrefix$key'];
      if (jsonString == null) return null;
      return jsonDecode(jsonString);
    } catch (e) {
      throw Exception('Failed to get cache data: $e');
    }
  }

  /// Remove data from localStorage
  Future<bool> remove(String key) async {
    try {
      _localStorage.remove('$_keyPrefix$key');
      return true;
    } catch (e) {
      throw Exception('Failed to remove cache data: $e');
    }
  }

  /// Clear all cache data
  Future<bool> clear() async {
    try {
      final keysToRemove = <String>[];
      for (final key in _localStorage.keys) {
        if (key.startsWith(_keyPrefix)) {
          keysToRemove.add(key);
        }
      }

      for (final key in keysToRemove) {
        _localStorage.remove(key);
      }

      return true;
    } catch (e) {
      throw Exception('Failed to clear cache: $e');
    }
  }

  /// Set data in sessionStorage
  Future<bool> setSession(String key, dynamic data) async {
    try {
      final jsonString = jsonEncode(data);
      _sessionStorage['$_keyPrefix$key'] = jsonString;
      return true;
    } catch (e) {
      throw Exception('Failed to set session data: $e');
    }
  }

  /// Get data from sessionStorage
  Future<dynamic> getSession(String key) async {
    try {
      final jsonString = _sessionStorage['$_keyPrefix$key'];
      if (jsonString == null) return null;
      return jsonDecode(jsonString);
    } catch (e) {
      throw Exception('Failed to get session data: $e');
    }
  }

  /// Clear sessionStorage
  Future<bool> clearSession() async {
    try {
      final keysToRemove = <String>[];
      for (final key in _sessionStorage.keys) {
        if (key.startsWith(_keyPrefix)) {
          keysToRemove.add(key);
        }
      }

      for (final key in keysToRemove) {
        _sessionStorage.remove(key);
      }

      return true;
    } catch (e) {
      throw Exception('Failed to clear session storage: $e');
    }
  }

  /// Set data in localStorage with expiration
  Future<bool> setLocal(String key, dynamic data,
      {Duration? expiration}) async {
    try {
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiration': expiration?.inMilliseconds,
      };

      final jsonString = jsonEncode(cacheData);
      _localStorage['$_keyPrefix$key'] = jsonString;
      return true;
    } catch (e) {
      throw Exception('Failed to set local data: $e');
    }
  }

  /// Get data from localStorage with expiration check
  Future<dynamic> getLocal(String key) async {
    try {
      final jsonString = _localStorage['$_keyPrefix$key'];
      if (jsonString == null) return null;

      final cacheData = jsonDecode(jsonString) as Map<String, dynamic>;
      final timestamp = cacheData['timestamp'] as int;
      final expiration = cacheData['expiration'] as int?;

      if (expiration != null) {
        final expirationTime = timestamp + expiration;
        if (DateTime.now().millisecondsSinceEpoch > expirationTime) {
          // Data has expired, remove it
          await removeLocal(key);
          return null;
        }
      }

      return cacheData['data'];
    } catch (e) {
      throw Exception('Failed to get local data: $e');
    }
  }

  /// Remove data from localStorage
  Future<bool> removeLocal(String key) async {
    try {
      _localStorage.remove('$_keyPrefix$key');
      return true;
    } catch (e) {
      throw Exception('Failed to remove local data: $e');
    }
  }

  /// Check if a key exists in localStorage
  bool hasKey(String key) {
    return _localStorage.containsKey('$_keyPrefix$key');
  }

  /// Check if a key exists in sessionStorage
  bool hasSessionKey(String key) {
    return _sessionStorage.containsKey('$_keyPrefix$key');
  }

  /// Get all keys with the app prefix
  List<String> getAllKeys() {
    return _localStorage.keys
        .where((key) => key.startsWith(_keyPrefix))
        .map((key) => key.substring(_keyPrefix.length))
        .toList();
  }

  /// Get cache size in bytes (approximate)
  int getCacheSize() {
    int totalSize = 0;
    for (final key in _localStorage.keys) {
      if (key.startsWith(_keyPrefix)) {
        final value = _localStorage[key] ?? '';
        totalSize += key.length + value.length;
      }
    }
    return totalSize;
  }

  /// Get session storage size in bytes (approximate)
  int getSessionSize() {
    int totalSize = 0;
    for (final key in _sessionStorage.keys) {
      if (key.startsWith(_keyPrefix)) {
        final value = _sessionStorage[key] ?? '';
        totalSize += key.length + value.length;
      }
    }
    return totalSize;
  }
}
